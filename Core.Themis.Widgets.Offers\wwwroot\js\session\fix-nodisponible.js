// Script pour forcer l'application des styles aux messages de non-disponibilité
function applyNodisponibleStyles() {
    // Attendre que le DOM soit prêt
    $(document).ready(function() {
        // Fonction pour appliquer les styles
        function forceNodisponibleStyles() {
            // Sélectionner tous les éléments avec la classe nodisponible
            var elements = $('#alleventhours .onehour.notavailable.nodisponible');

            if (elements.length > 0) {
                console.log('Applying styles to nodisponible elements:', elements.length);

                elements.each(function() {
                    var $el = $(this);

                    // Appliquer les styles directement via JavaScript
                    $el.css({
                        'border-radius': '8px !important',
                        'background-color': '#f8f9fa !important',
                        'background-image': 'none !important',
                        'border': '1px solid #dee2e6 !important',
                        'padding': '12px 15px !important',
                        'margin': '10px auto !important',
                        'display': 'block !important',
                        'width': '90% !important',
                        'max-width': '600px !important',
                        'text-align': 'center !important',
                        'white-space': 'normal !important',
                        'word-wrap': 'break-word !important',
                        'line-height': '1.5 !important',
                        'font-size': '14px !important',
                        'color': '#495057 !important',
                        'font-weight': '500 !important',
                        'box-shadow': '0 1px 3px rgba(0,0,0,0.1) !important',
                        'cursor': 'default !important',
                        'transition': 'none !important',
                        'position': 'static !important',
                        'overflow': 'visible !important'
                    });

                    // Supprimer les classes qui pourraient interférer
                    $el.removeClass('btn btn-primary btn-secondary');
                });
            }

            // Traiter également l'élément #oneManifs qui peut contenir des messages de non-disponibilité
            var oneManifs = $('#oneManifs');
            if (oneManifs.length > 0 && oneManifs.text().trim() !== '') {
                console.log('Applying styles to #oneManifs element');

                // Appliquer les styles pour transformer le bouton en zone de texte
                oneManifs.css({
                    'border-radius': '8px !important',
                    'background-color': '#f8f9fa !important',
                    'background-image': 'none !important',
                    'border': '1px solid #dee2e6 !important',
                    'padding': '15px 20px !important',
                    'margin': '15px auto !important',
                    'display': 'block !important',
                    'width': '90% !important',
                    'max-width': '600px !important',
                    'text-align': 'center !important',
                    'white-space': 'normal !important',
                    'word-wrap': 'break-word !important',
                    'line-height': '1.5 !important',
                    'font-size': '14px !important',
                    'color': '#495057 !important',
                    'font-weight': '500 !important',
                    'box-shadow': '0 1px 3px rgba(0,0,0,0.1) !important',
                    'cursor': 'default !important',
                    'transition': 'none !important',
                    'position': 'static !important',
                    'overflow': 'visible !important'
                });

                // Supprimer les classes Bootstrap qui donnent l'apparence de bouton
                oneManifs.removeClass('btn btn-primary btn-secondary btn-light btn-dark btn-outline-primary btn-outline-secondary');
            }

            // Recherche globale de tous les éléments qui contiennent le message de non-disponibilité
            var searchTexts = [
                'Aktuell kein Onlineverkauf',
                'An Vorstellungstagen bitte direkt',
                'Plus de place disponible',
                'No tickets available'
            ];

            searchTexts.forEach(function(searchText) {
                $('*').filter(function() {
                    return $(this).text().indexOf(searchText) !== -1 &&
                           $(this).children().length === 0; // Seulement les éléments feuilles
                }).each(function() {
                    var $el = $(this);
                    console.log('Found element with message:', searchText, $el);

                    // Appliquer les styles de zone de texte
                    $el.css({
                        'border-radius': '8px !important',
                        'background-color': '#f8f9fa !important',
                        'background-image': 'none !important',
                        'border': '1px solid #dee2e6 !important',
                        'padding': '15px 20px !important',
                        'margin': '15px auto !important',
                        'display': 'block !important',
                        'width': '90% !important',
                        'max-width': '600px !important',
                        'text-align': 'center !important',
                        'white-space': 'normal !important',
                        'word-wrap': 'break-word !important',
                        'line-height': '1.5 !important',
                        'font-size': '14px !important',
                        'color': '#495057 !important',
                        'font-weight': '500 !important',
                        'box-shadow': '0 1px 3px rgba(0,0,0,0.1) !important',
                        'cursor': 'default !important',
                        'transition': 'none !important',
                        'position': 'static !important',
                        'overflow': 'visible !important'
                    });

                    // Supprimer toutes les classes Bootstrap de bouton
                    $el.removeClass('btn btn-primary btn-secondary btn-light btn-dark btn-outline-primary btn-outline-secondary btn-success btn-danger btn-warning btn-info');
                });
            });
        }
        
        // Appliquer les styles immédiatement
        forceNodisponibleStyles();
        
        // Réappliquer les styles après un délai (au cas où d'autres scripts modifient le DOM)
        setTimeout(forceNodisponibleStyles, 500);
        setTimeout(forceNodisponibleStyles, 1000);
        
        // Observer les changements dans le DOM pour réappliquer les styles si nécessaire
        if (window.MutationObserver) {
            var observer = new MutationObserver(function(mutations) {
                var shouldReapply = false;
                mutations.forEach(function(mutation) {
                    if (mutation.type === 'childList') {
                        // Vérifier si des éléments nodisponible ont été ajoutés
                        $(mutation.addedNodes).each(function() {
                            if ($(this).hasClass && $(this).hasClass('nodisponible')) {
                                shouldReapply = true;
                            }
                        });
                    }
                });
                
                if (shouldReapply) {
                    setTimeout(forceNodisponibleStyles, 100);
                }
            });
            
            // Observer les changements dans le conteneur des heures
            var target = document.getElementById('alleventhours');
            if (target) {
                observer.observe(target, { childList: true, subtree: true });
            }

            // Observer également les changements dans #oneManifs
            var oneManifs = document.getElementById('oneManifs');
            if (oneManifs) {
                observer.observe(oneManifs, { childList: true, subtree: true });
            }
        }
    });
}

// Fonction à appeler après le chargement de LESS
function lessReadyfunc() {
    console.log('LESS is ready, applying nodisponible styles');
    applyNodisponibleStyles();
}

// Si LESS n'est pas encore chargé, attendre
if (typeof less !== 'undefined' && less.pageLoadFinished) {
    less.pageLoadFinished.then(function() {
        applyNodisponibleStyles();
    });
} else {
    // Fallback si LESS n'est pas disponible
    applyNodisponibleStyles();
}
