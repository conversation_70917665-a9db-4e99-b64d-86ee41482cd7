// Script simple pour corriger l'affichage des messages de non-disponibilité
$(document).ready(function() {
    console.log('Fix nodisponible: Script chargé');

    // Fonction simple pour corriger les éléments .noevent
    function fixNoeventElements() {
        $('#alleventhours .noevent').each(function() {
            var $el = $(this);
            console.log('Fix nodisponible: Élément .noevent trouvé', $el.text());

            // Appliquer les styles pour permettre l'affichage complet du texte
            $el.css({
                'height': 'auto',
                'line-height': '1.4',
                'white-space': 'normal',
                'word-wrap': 'break-word',
                'padding': '10px 15px',
                'min-height': '40px'
            });
        });
    }


    // Appliquer la correction immédiatement
    fixNoeventElements();

    // Réappliquer après un délai pour les éléments chargés dynamiquement
    setTimeout(fixNoeventElements, 500);
    setTimeout(fixNoeventElements, 1000);

    // Observer les changements DOM pour les nouveaux éléments
    if (window.MutationObserver) {
        var observer = new MutationObserver(function(mutations) {
            var shouldReapply = false;
            mutations.forEach(function(mutation) {
                if (mutation.type === 'childList') {
                    $(mutation.addedNodes).each(function() {
                        if ($(this).hasClass && $(this).hasClass('noevent')) {
                            shouldReapply = true;
                        }
                    });
                }
            });

            if (shouldReapply) {
                setTimeout(fixNoeventElements, 100);
            }
        });

        var target = document.getElementById('alleventhours');
        if (target) {
            observer.observe(target, { childList: true, subtree: true });
        }
    }
});
