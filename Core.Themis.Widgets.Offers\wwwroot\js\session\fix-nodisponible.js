// Script pour forcer l'application des styles aux messages de non-disponibilité
function applyNodisponibleStyles() {
    // Attendre que le DOM soit prêt
    $(document).ready(function() {
        // Fonction pour appliquer les styles
        function forceNodisponibleStyles() {
            // Sélectionner tous les éléments avec la classe nodisponible
            var elements = $('#alleventhours .onehour.notavailable.nodisponible');

            if (elements.length > 0) {
                console.log('Applying styles to nodisponible elements:', elements.length);

                elements.each(function() {
                    var $el = $(this);

                    // Appliquer les styles directement via JavaScript
                    $el.css({
                        'border-radius': '8px !important',
                        'background-color': '#f8f9fa !important',
                        'background-image': 'none !important',
                        'border': '1px solid #dee2e6 !important',
                        'padding': '12px 15px !important',
                        'margin': '10px auto !important',
                        'display': 'block !important',
                        'width': '90% !important',
                        'max-width': '600px !important',
                        'text-align': 'center !important',
                        'white-space': 'normal !important',
                        'word-wrap': 'break-word !important',
                        'line-height': '1.5 !important',
                        'font-size': '14px !important',
                        'color': '#495057 !important',
                        'font-weight': '500 !important',
                        'box-shadow': '0 1px 3px rgba(0,0,0,0.1) !important',
                        'cursor': 'default !important',
                        'transition': 'none !important',
                        'position': 'static !important',
                        'overflow': 'visible !important'
                    });

                    // Supprimer les classes qui pourraient interférer
                    $el.removeClass('btn btn-primary btn-secondary');
                });
            }

            // Traiter également l'élément #oneManifs qui peut contenir des messages de non-disponibilité
            var oneManifs = $('#oneManifs');
            if (oneManifs.length > 0 && oneManifs.text().trim() !== '') {
                console.log('Applying styles to #oneManifs element');

                // Appliquer les styles pour transformer le bouton en zone de texte
                oneManifs.css({
                    'border-radius': '8px !important',
                    'background-color': '#f8f9fa !important',
                    'background-image': 'none !important',
                    'border': '1px solid #dee2e6 !important',
                    'padding': '15px 20px !important',
                    'margin': '15px auto !important',
                    'display': 'block !important',
                    'width': '90% !important',
                    'max-width': '600px !important',
                    'text-align': 'center !important',
                    'white-space': 'normal !important',
                    'word-wrap': 'break-word !important',
                    'line-height': '1.5 !important',
                    'font-size': '14px !important',
                    'color': '#495057 !important',
                    'font-weight': '500 !important',
                    'box-shadow': '0 1px 3px rgba(0,0,0,0.1) !important',
                    'cursor': 'default !important',
                    'transition': 'none !important',
                    'position': 'static !important',
                    'overflow': 'visible !important'
                });

                // Supprimer les classes Bootstrap qui donnent l'apparence de bouton
                oneManifs.removeClass('btn btn-primary btn-secondary btn-light btn-dark btn-outline-primary btn-outline-secondary');
            }

            // Recherche globale de tous les éléments qui contiennent le message de non-disponibilité
            var searchTexts = [
                'Aktuell kein Onlineverkauf',
                'An Vorstellungstagen bitte direkt',
                'Plus de place disponible',
                'No tickets available'
            ];

            searchTexts.forEach(function(searchText) {
                // Recherche plus large incluant les éléments avec des enfants
                $('*').filter(function() {
                    var text = $(this).text().trim();
                    return text.indexOf(searchText) !== -1 && text.length > 0;
                }).each(function() {
                    var $el = $(this);

                    // Éviter de traiter les éléments parents si un enfant contient déjà le texte
                    var hasChildWithText = $el.find('*').filter(function() {
                        return $(this).text().indexOf(searchText) !== -1;
                    }).length > 0;

                    if (!hasChildWithText || $el.children().length === 0) {
                        console.log('Found element with message:', searchText, $el);

                        // Appliquer les styles de zone de texte
                        $el.css({
                            'border-radius': '8px !important',
                            'background-color': '#f8f9fa !important',
                            'background-image': 'none !important',
                            'border': '1px solid #dee2e6 !important',
                            'padding': '15px 20px !important',
                            'margin': '15px auto !important',
                            'display': 'block !important',
                            'width': '90% !important',
                            'max-width': '600px !important',
                            'text-align': 'center !important',
                            'white-space': 'normal !important',
                            'word-wrap': 'break-word !important',
                            'line-height': '1.5 !important',
                            'font-size': '14px !important',
                            'color': '#495057 !important',
                            'font-weight': '500 !important',
                            'box-shadow': '0 1px 3px rgba(0,0,0,0.1) !important',
                            'cursor': 'default !important',
                            'transition': 'none !important',
                            'position': 'static !important',
                            'overflow': 'visible !important'
                        });

                        // Supprimer toutes les classes Bootstrap de bouton
                        $el.removeClass('btn btn-primary btn-secondary btn-light btn-dark btn-outline-primary btn-outline-secondary btn-success btn-danger btn-warning btn-info');
                    }
                });
            });

            // Traitement spécifique pour les boutons avec les messages de non-disponibilité
            $('button, .btn, [class*="btn"]').filter(function() {
                var text = $(this).text().trim();
                return searchTexts.some(function(searchText) {
                    return text.indexOf(searchText) !== -1;
                });
            }).each(function() {
                var $btn = $(this);
                console.log('Found button with nodisponible message:', $btn);

                // Transformer le bouton en zone de texte
                $btn.css({
                    'border-radius': '8px !important',
                    'background-color': '#f8f9fa !important',
                    'background-image': 'none !important',
                    'border': '1px solid #dee2e6 !important',
                    'padding': '15px 20px !important',
                    'margin': '15px auto !important',
                    'display': 'block !important',
                    'width': '90% !important',
                    'max-width': '600px !important',
                    'text-align': 'center !important',
                    'white-space': 'normal !important',
                    'word-wrap': 'break-word !important',
                    'line-height': '1.5 !important',
                    'font-size': '14px !important',
                    'color': '#495057 !important',
                    'font-weight': '500 !important',
                    'box-shadow': '0 1px 3px rgba(0,0,0,0.1) !important',
                    'cursor': 'default !important',
                    'transition': 'none !important',
                    'position': 'static !important',
                    'overflow': 'visible !important'
                });

                // Supprimer toutes les classes de bouton
                $btn.removeClass('btn btn-primary btn-secondary btn-light btn-dark btn-outline-primary btn-outline-secondary btn-success btn-danger btn-warning btn-info');

                // Désactiver les événements de clic
                $btn.off('click').on('click', function(e) {
                    e.preventDefault();
                    return false;
                });

                // Marquer l'élément avec un attribut pour le CSS
                $btn.attr('data-nodisponible', 'true');
            });

            // Marquer tous les éléments contenant les messages avec l'attribut data-nodisponible
            searchTexts.forEach(function(searchText) {
                $('*').filter(function() {
                    var text = $(this).text().trim();
                    return text.indexOf(searchText) !== -1 && text.length > 0;
                }).attr('data-nodisponible', 'true');
            });
        }
        
        // Appliquer les styles immédiatement
        forceNodisponibleStyles();
        
        // Réappliquer les styles après un délai (au cas où d'autres scripts modifient le DOM)
        setTimeout(forceNodisponibleStyles, 500);
        setTimeout(forceNodisponibleStyles, 1000);
        
        // Observer les changements dans le DOM pour réappliquer les styles si nécessaire
        if (window.MutationObserver) {
            var observer = new MutationObserver(function(mutations) {
                var shouldReapply = false;
                mutations.forEach(function(mutation) {
                    if (mutation.type === 'childList' || mutation.type === 'attributes') {
                        // Vérifier si des éléments avec des messages de non-disponibilité ont été ajoutés ou modifiés
                        $(mutation.addedNodes).each(function() {
                            if ($(this).hasClass && ($(this).hasClass('nodisponible') || $(this).hasClass('notavailable'))) {
                                shouldReapply = true;
                            }
                            // Vérifier aussi le contenu textuel
                            var text = $(this).text && $(this).text();
                            if (text && (text.indexOf('Aktuell kein Onlineverkauf') !== -1 ||
                                        text.indexOf('An Vorstellungstagen bitte direkt') !== -1 ||
                                        text.indexOf('Plus de place disponible') !== -1)) {
                                shouldReapply = true;
                            }
                        });
                    }
                });

                if (shouldReapply) {
                    setTimeout(forceNodisponibleStyles, 100);
                }
            });

            // Observer les changements dans plusieurs conteneurs
            var targets = ['alleventhours', 'oneManifs', 'wdgInsertGrilleTarif', 'sessionContainer'];
            targets.forEach(function(targetId) {
                var target = document.getElementById(targetId);
                if (target) {
                    observer.observe(target, {
                        childList: true,
                        subtree: true,
                        attributes: true,
                        attributeFilter: ['class']
                    });
                }
            });
        }
    });
}

// Fonction à appeler après le chargement de LESS
function lessReadyfunc() {
    console.log('LESS is ready, applying nodisponible styles');
    applyNodisponibleStyles();
}

// Si LESS n'est pas encore chargé, attendre
if (typeof less !== 'undefined' && less.pageLoadFinished) {
    less.pageLoadFinished.then(function() {
        applyNodisponibleStyles();
    });
} else {
    // Fallback si LESS n'est pas disponible
    applyNodisponibleStyles();
}
