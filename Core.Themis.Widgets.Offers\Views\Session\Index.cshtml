@using Core.Themis.Libraries.Razor.Areas.Session.ViewModels;
@using Core.Themis.Libraries.Razor.Areas.Session;
@using Core.Themis.Libraries.DTO.Translations;
@using Core.Themis.Widgets.Offers.Helpers;

@model CategPriceViewModel

@{
    ViewBag.Title = "Session";
    string UrlToPath = $"{@Context.Request.Scheme}://{@Context.Request.Host}{@Context.Request.PathBase}";

    if (UrlToPath.Contains("localhost:"))
    {
        UrlToPath = "https://localhost:44310/";
    }

    List<TranslationTermDTO> TranslationsList = ViewBag.TranslationsList as List<TranslationTermDTO>;
}

@section styles {
    <link rel="stylesheet/less" type="text/css" href="@(UrlToPath)css/Session/style.less">
    <link rel="stylesheet" type="text/css" href="@(UrlToPath)css/Session/style-override.css">
}

<component type="typeof(SessionApp)" render-mode="Server" param-AppSettings="@Model" />

@section scripts {
    <script>
        var htmlSelector = "@ViewBag.HtmlSelector";
        var structureId = "@ViewBag.StructureId";
        var langCode = "@ViewBag.LangCode";
        var eventId = "@ViewBag.EventId";
        var identityId = "@ViewBag.IdentityId";
        var webUserId = "@ViewBag.WebUserId";
        var buyerProfilId = "@ViewBag.BuyerProfilId";
        var partnerToken = "@ViewBag.PartnerToken";
        var ForceDate = "@ViewBag.ForceDate";
        var ForceSession = "@ViewBag.ForceSession";
        var sessionsData = @Html.Raw(ViewBag.SessionsData);
        var TranslationsList = @Html.Raw(Json.Serialize(TranslationsList));
        var SettingsMerge = @Html.Raw(ViewBag.SettingsMerge);
        var sWOffersUrl = "@ViewBag.WOfferUrl";
        var widgetCatalogUrl = "@ViewBag.WCatalogUrl";
    </script>
    <script src="@(UrlToPath)js/session/session.js"></script>
    <script src="@(UrlToPath)js/session/fix-nodisponible.js"></script>
    <script>
        // Fonction lessReady pour déclencher notre script de correction
        function lessReady() {
            console.log('lessReady called - applying nodisponible fix');
            // Le script fix-nodisponible.js s'exécute automatiquement
        }
    </script>
}
