# Fix simple pour les messages de non-disponibilité tronqués

## Problème
Les messages de non-disponibilité comme "Aktuell kein Onlineverkauf. An Vorstellungstagen bitte direkt an die Theaterkasse (069 / 28 45 80) wenden" étaient affichés dans des éléments `.noevent` avec une hauteur fixe de 40px, ce qui tronquait le texte long.

## Solution Simple
La solution corrige uniquement le style CSS qui cause la troncature en permettant à l'élément de s'adapter à la hauteur du contenu.

### Fichiers modifiés :

1. **Core.Themis.Widgets.Offers/wwwroot/css/Session/style-override.css** (simplifié)
   - Correction ciblée pour les éléments `.noevent`
   - Suppression de la hauteur fixe et ajout du retour à la ligne

2. **Core.Themis.Widgets.Offers/wwwroot/js/session/fix-nodisponible.js** (simplifié)
   - Script minimal pour appliquer les corrections CSS
   - Observateur DOM pour les éléments ajoutés dynamiquement

### Correction appliquée :

#### CSS
```css
#alleventhours .noevent {
    height: auto !important;
    line-height: 1.4 !important;
    white-space: normal !important;
    word-wrap: break-word !important;
    padding: 10px 15px !important;
    min-height: 40px !important;
}
```

#### JavaScript
- Détection des éléments `.noevent`
- Application des styles pour permettre l'affichage complet
- Surveillance des changements DOM

### Résultat :
- **Avant** : Texte tronqué dans un élément de 40px de hauteur
- **Après** : Texte complet visible avec hauteur automatique

### Test
Un fichier de test `test_nodisponible_fix.html` reproduit le problème et montre la correction.

## Utilisation
La solution s'active automatiquement. Il suffit d'inclure les fichiers CSS et JavaScript dans la page.

## Compatibilité
- Minimal et non-intrusif
- Compatible avec le design existant
- Fonctionne avec jQuery
