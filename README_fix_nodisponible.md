# Fix simple pour les messages de non-disponibilité tronqués

## Problème
Les messages de non-disponibilité comme "Aktuell kein Onlineverkauf. An Vorstellungstagen bitte direkt an die Theaterkasse (069 / 28 45 80) wenden" étaient affichés dans des éléments `.noevent` avec une hauteur fixe de 40px, ce qui tronquait le texte long.

## Solution Simple
La solution transforme l'élément bouton en texte simple, supprimant complètement l'apparence de bouton et permettant l'affichage complet du message.

### Fichiers modifiés :

1. **Core.Themis.Widgets.Offers/wwwroot/css/Session/style-override.css** (simplifié)
   - Correction ciblée pour les éléments `.noevent`
   - Suppression de la hauteur fixe et ajout du retour à la ligne

2. **Core.Themis.Widgets.Offers/wwwroot/js/session/fix-nodisponible.js** (simplifié)
   - Script minimal pour appliquer les corrections CSS
   - Observateur DOM pour les éléments ajoutés dynamiquement

### Correction appliquée :

#### CSS
```css
#alleventhours .noevent {
    background: none !important;        /* Supprime le fond gris */
    border: none !important;            /* Supprime les bordures */
    border-radius: 0 !important;        /* Supprime les coins arrondis */
    box-shadow: none !important;        /* Supprime l'ombre */
    height: auto !important;            /* Hauteur automatique */
    line-height: 1.4 !important;        /* Espacement normal */
    white-space: normal !important;     /* Retour à la ligne autorisé */
    word-wrap: break-word !important;   /* Coupure des mots longs */
    padding: 5px 0 !important;          /* Espacement minimal */
    color: #666 !important;             /* Couleur de texte grise */
    font-size: 14px !important;         /* Taille de police */
    text-align: center !important;      /* Centrage du texte */
    cursor: default !important;         /* Curseur normal */
}
```

#### JavaScript
- Détection des éléments `.noevent`
- Application des styles pour permettre l'affichage complet
- Surveillance des changements DOM

### Résultat :
- **Avant** : Texte tronqué dans un bouton gris avec fond et bordures
- **Après** : Texte complet affiché comme du texte simple (sans apparence de bouton)

### Test
Un fichier de test `test_nodisponible_fix.html` reproduit le problème et montre la correction.

## Utilisation
La solution s'active automatiquement. Il suffit d'inclure les fichiers CSS et JavaScript dans la page.

## Compatibilité
- Minimal et non-intrusif
- Compatible avec le design existant
- Fonctionne avec jQuery
