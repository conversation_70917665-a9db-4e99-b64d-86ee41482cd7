<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Fix Nodisponible - Version Simple</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <style>
        body {
            padding: 20px;
            font-family: Arial, sans-serif;
        }
        .test-section {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-title {
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
        }

        /* Style original qui cause le problème */
        #alleventhours .noevent {
            background: #bbb;
            color: #fff;
            content: "";
            height: 40px;
            line-height: 40px;
            padding: 0 6%;
            display: block;
            border-radius: 1000px;
        }
    </style>
</head>
<body>
    <h1>Test de la correction simple des messages de non-disponibilité</h1>

    <div class="test-section">
        <div class="test-title">Test: Élément .noevent avec texte long (reproduit le problème)</div>
        <div id="alleventhours">
            <span class="noevent">
                Aktuell kein Onlineverkauf. An Vorstellungstagen bitte direkt an die Theaterkasse (069 / 28 45 80) wenden
            </span>
        </div>
        <p><strong>Avant correction:</strong> Le texte est tronqué à cause de height: 40px</p>
        <p><strong>Après correction:</strong> Le texte complet est visible</p>
    </div>

    <div class="test-section">
        <div class="test-title">Test: Ajout dynamique d'un élément .noevent</div>
        <div id="dynamic-container"></div>
        <button onclick="addDynamicElement()">Ajouter un élément .noevent dynamique</button>
    </div>

    <!-- Inclusion des styles CSS de correction -->
    <link rel="stylesheet" type="text/css" href="Core.Themis.Widgets.Offers/wwwroot/css/Session/style-override.css">

    <!-- Inclusion du script de correction -->
    <script src="Core.Themis.Widgets.Offers/wwwroot/js/session/fix-nodisponible.js"></script>

    <script>
        // Fonction pour ajouter un élément dynamique
        function addDynamicElement() {
            var container = document.getElementById('dynamic-container');
            var alleventhours = document.createElement('div');
            alleventhours.id = 'alleventhours-dynamic';
            var newElement = document.createElement('span');
            newElement.className = 'noevent';
            newElement.textContent = 'Aktuell kein Onlineverkauf. An Vorstellungstagen bitte direkt an die Theaterkasse (069 / 28 45 80) wenden - Élément ajouté dynamiquement';
            alleventhours.appendChild(newElement);
            container.appendChild(alleventhours);
        }
    </script>
</body>
</html>
