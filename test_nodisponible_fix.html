<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Fix Nodisponible</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <style>
        body {
            padding: 20px;
            font-family: Arial, sans-serif;
        }
        .test-section {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-title {
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
        }
    </style>
</head>
<body>
    <h1>Test de la correction des messages de non-disponibilité</h1>
    
    <div class="test-section">
        <div class="test-title">Test 1: Bouton avec message allemand (comme dans la capture d'écran)</div>
        <button class="btn btn-primary" id="test-btn-1">
            Aktuell kein Onlineverkauf. An Vorstellungstagen bitte direkt an die Theaterkasse (069 / 28 45 80) wenden
        </button>
    </div>
    
    <div class="test-section">
        <div class="test-title">Test 2: Élément avec classe nodisponible</div>
        <div class="onehour notavailable nodisponible" id="test-div-1">
            Plus de place disponible pour cette séance
        </div>
    </div>
    
    <div class="test-section">
        <div class="test-title">Test 3: Élément dans #alleventhours</div>
        <div id="alleventhours">
            <div class="onehour notavailable nodisponible">
                Aktuell kein Onlineverkauf. An Vorstellungstagen bitte direkt an die Theaterkasse
            </div>
        </div>
    </div>
    
    <div class="test-section">
        <div class="test-title">Test 4: Élément dans #oneManifs</div>
        <div id="oneManifs">
            No tickets available for this session. Please contact the box office directly.
        </div>
    </div>
    
    <div class="test-section">
        <div class="test-title">Test 5: Ajout dynamique d'éléments</div>
        <div id="dynamic-container"></div>
        <button onclick="addDynamicElement()" class="btn btn-secondary">Ajouter un élément dynamique</button>
    </div>

    <!-- Inclusion des styles CSS de correction -->
    <link rel="stylesheet" type="text/css" href="Core.Themis.Widgets.Offers/wwwroot/css/Session/style-override.css">
    
    <!-- Inclusion du script de correction -->
    <script src="Core.Themis.Widgets.Offers/wwwroot/js/session/fix-nodisponible.js"></script>
    
    <script>
        // Fonction pour ajouter un élément dynamique
        function addDynamicElement() {
            var container = document.getElementById('dynamic-container');
            var newElement = document.createElement('button');
            newElement.className = 'btn btn-warning';
            newElement.textContent = 'Aktuell kein Onlineverkauf - Élément ajouté dynamiquement';
            container.appendChild(newElement);
        }
        
        // Initialiser les styles après le chargement de la page
        $(document).ready(function() {
            console.log('Page chargée, application des styles...');
            
            // Simuler le chargement de LESS (pour les tests)
            if (typeof applyNodisponibleStyles === 'function') {
                applyNodisponibleStyles();
            }
        });
    </script>
</body>
</html>
